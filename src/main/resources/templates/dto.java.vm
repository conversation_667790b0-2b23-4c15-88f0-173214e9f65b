package $!{dtoPackage};

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
#if($!{existsValidate})
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
#end
import java.io.Serializable;
#if($!{existsBigDecimalType})
import java.math.BigDecimal;
#end
#if($!{existsTimeType})
import java.sql.Time;
#end
#if($!{existsDateType})
import java.util.Date;
#end

/**
 * $!{tableComment}参数
 *
 * <AUTHOR>
 * @since $!{date}
 */
@Data
@Schema(description = "$!{tableComment}参数")
public class $!{dtoName} implements Serializable {

    private static final long serialVersionUID = 1L;
#foreach($column in $!{columns})
#if(!$!{commonExcludeFields.contains($!{column.propertyName})})

    #if($!{column.columnComment} != "")
    @Schema(description = "$!{column.columnComment}")
    #end
    #if($!{column.isValidate})
    #if($!{column.propertyType} == 'String')
    @NotBlank(message = "$!{column.columnCustomComment}不能为空")
    #if($!{column.existsLength})
    @Length(max = $!{column.columnLength}, message = "$!{column.columnComment}长度超过限制")
    #end
    #else
    @NotNull(message = "$!{column.columnCustomComment}不能为空")
    #end
    #end
    private $!{column.propertyType} $!{column.propertyName};
#end
#end

}


