package $!{controllerPackage};

#if($!{enablePermission})
import $!{permissionPackagePath};
#end
#if($!{enableLog})
import $!{sysLogTypePackagePath};
import $!{logPackagePath};
#end
import $!{pagingPackagePath};
import $!{apiResultPackagePath};
import $!{dtoPackagePath};
import $!{queryPackagePath};
import $!{servicePackagePath};
import $!{voPackagePath};
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
#if($!{requestMappingStyle} == 2)
import $!{parameterObjectPackagePath};
#end
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
#if($!{existsSerializableType})
import java.io.Serializable;
#end

/**
 * $!{tableComment} 控制器
 *
 * <AUTHOR>
 * @since $!{date}
 */
@Slf4j
@RestController
@Tag(name = "$!{tableComment}")
$!{controllerRequestMapping}
public class $!{controllerName} {

    @Autowired
    private $!{serviceName} $!{serviceObjectName};

    /**
     * $!{addCnComment}
     *
     * @param $!{dtoObjectName}
     * @return
     * @throws Exception
     */
    #if($!{enableLog})
    @Log(type = SysLogType.ADD)
    #end
    @Operation(summary = "$!{addCnComment}")
    $!{addRequestMapping}
    #if($!{enablePermission})
    @Permission("$!{addPermissionCode}")
    #end
    public ApiResult $!{addMethodName}(@Valid @RequestBody $!{dtoName} $!{dtoObjectName}) {
        log.info("$!{addCnComment}：{}", $!{dtoObjectName});
        boolean flag = $!{serviceObjectName}.$!{addMethodName}($!{dtoObjectName});
        return ApiResult.result(flag);
    }

    /**
     * $!{updateCnComment}
     *
     * @param $!{dtoObjectName}
     * @return
     * @throws Exception
     */
    #if($!{enableLog})
    @Log(type = SysLogType.UPDATE)
    #end
    @Operation(summary = "$!{updateCnComment}")
    $!{updateRequestMapping}
    #if($!{enablePermission})
    @Permission("$!{updatePermissionCode}")
    #end
    public ApiResult $!{updateMethodName}(@Valid @RequestBody $!{dtoName} $!{dtoObjectName}) {
        log.info("$!{updateCnComment}：{}", $!{dtoObjectName});
        boolean flag = $!{serviceObjectName}.$!{updateMethodName}($!{dtoObjectName});
        return ApiResult.result(flag);
    }

    /**
     * $!{deleteCnComment}
     *
     * @param $!{pkIdPropertyName}
     * @return
     * @throws Exception
     */
    #if($!{enableLog})
    @Log(type = SysLogType.DELETE)
    #end
    @Operation(summary = "$!{deleteCnComment}")
    $!{deleteRequestMapping}
    #if($!{enablePermission})
    @Permission("$!{deletePermissionCode}")
    #end
    public ApiResult $!{deleteMethodName}(@PathVariable $!{pkIdPropertyType} $!{pkIdPropertyName}) {
        log.info("$!{deleteCnComment}：{}", $!{pkIdPropertyName});
        boolean flag = $!{serviceObjectName}.$!{deleteMethodName}($!{pkIdPropertyName});
        return ApiResult.result(flag);
    }

    /**
     * $!{infoCnComment}
     *
     * @param $!{pkIdPropertyName}
     * @return
     * @throws Exception
     */
    @Operation(summary = "$!{infoCnComment}")
    $!{infoRequestMapping}
    #if($!{enablePermission})
    @Permission("$!{infoPermissionCode}")
    #end
    public ApiResult<$!{voName}> $!{infoMethodName}(@PathVariable $!{pkIdPropertyType} $!{pkIdPropertyName}) {
        log.info("$!{infoCnComment}：{}", $!{pkIdPropertyName});
        $!{voName} $!{voObjectName} = $!{serviceObjectName}.$!{infoMethodName}ById($!{pkIdPropertyName});
        return ApiResult.success($!{voObjectName});
    }

    /**
     * $!{pageCnComment}
     *
     * @param $!{queryObjectName}
     * @return
     * @throws Exception
     */
    @Operation(summary = "$!{pageCnComment}")
    $!{pageRequestMapping}
    #if($!{enablePermission})
    @Permission("$!{pagePermissionCode}")
    #end
    public ApiResult<$!{voName}> $!{pageMethodName}(@Valid $!{pageParamAnnotation} $!{queryName} $!{queryObjectName}) {
        log.info("$!{pageCnComment}：{}", $!{queryObjectName});
        Paging<$!{voName}> paging = $!{serviceObjectName}.$!{pageMethodName}($!{queryObjectName});
        return ApiResult.success(paging);
    }

}
