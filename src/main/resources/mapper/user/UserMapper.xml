<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.geekidea.boot.user.mapper.UserMapper">

    <select id="getUserById" resultType="io.geekidea.boot.user.vo.UserVo">
        select *
        from user
        where id = #{id}
    </select>

    <select id="getUserPage" parameterType="io.geekidea.boot.user.query.UserQuery"
            resultType="io.geekidea.boot.user.vo.UserVo">
        select *
        from user
    </select>

    <select id="getAppUserById" resultType="io.geekidea.boot.user.vo.AppUserVo">
        select u.*, r.name userRoleName
        from user u
                 left join user_role r
                            on u.user_role_id = r.id
        where u.id = #{id}
    </select>

</mapper>
