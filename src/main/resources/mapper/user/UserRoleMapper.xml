<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.geekidea.boot.user.mapper.UserRoleMapper">

    <select id="getUserRoleById" resultType="io.geekidea.boot.user.vo.UserRoleVo">
        select *
        from user_role
            where id = #{id}
    </select>

    <select id="getUserRolePage" parameterType="io.geekidea.boot.user.query.UserRoleQuery"
            resultType="io.geekidea.boot.user.vo.UserRoleVo">
        select *
        from user_role
    </select>

</mapper>
