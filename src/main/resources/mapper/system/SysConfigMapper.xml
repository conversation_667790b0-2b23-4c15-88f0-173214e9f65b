<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.geekidea.boot.system.mapper.SysConfigMapper">

    <select id="getSysConfigById" resultType="io.geekidea.boot.system.vo.SysConfigVo">
        select *
        from sys_config
        where id = #{id}
    </select>

    <select id="getSysConfigPage" parameterType="io.geekidea.boot.system.query.SysConfigQuery"
            resultType="io.geekidea.boot.system.vo.SysConfigVo">
        select *
        from sys_config
        <where>
            <if test="keyword != null and keyword != ''">
                and (config_name like concat('%',#{keyword},'%')
                or config_key like concat('%',#{keyword},'%')
                or config_value like concat('%',#{keyword},'%'))
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="isSystem != null">
                and is_system = #{isSystem}
            </if>
        </where>
    </select>

</mapper>
