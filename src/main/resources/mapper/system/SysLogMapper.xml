<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.geekidea.boot.system.mapper.SysLogMapper">

    <select id="getSysLogById" resultType="io.geekidea.boot.system.vo.SysLogVo">
        select *
        from sys_log
        where id = #{id}
    </select>

    <select id="getSysLogPage" parameterType="io.geekidea.boot.system.query.SysLogQuery"
            resultType="io.geekidea.boot.system.vo.SysLogVo">
        select *
        from sys_log
        <where>
            <if test="moduleName != null and moduleName != ''">
                module_name like concat('%',#{moduleName},'%')
            </if>
            <if test="requestUrl != null and requestUrl != ''">
                and request_url like concat('%',#{requestUrl},'%')
            </if>
            <if test="logName != null and logName != ''">
                and log_name like concat('%',#{logName},'%')
            </if>
            <if test="username != null and username != ''">
                and username like concat('%',#{username},'%')
            </if>
            <if test="logType != null">
                and log_type = #{logType}
            </if>
            <if test="responseSuccess != null">
                and response_success = #{responseSuccess}
            </if>
            <if test="requestIp != null and requestIp != ''">
                and request_ip like concat('%',#{requestIp},'%')
            </if>
            <if test="createStartTime != null">
                and date(create_time) >= date(#{createStartTime})
            </if>
            <if test="createEndTime != null">
                and date(create_time) &lt;= date(#{createEndTime})
            </if>
        </where>
    </select>

</mapper>
