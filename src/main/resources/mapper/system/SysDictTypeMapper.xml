<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.geekidea.boot.system.mapper.SysDictTypeMapper">

    <select id="getSysDictTypeById" resultType="io.geekidea.boot.system.vo.SysDictTypeVo">
        select *
        from sys_dict_type
        where id = #{id}
    </select>

    <select id="getSysDictTypeList" parameterType="io.geekidea.boot.system.query.SysDictTypeQuery"
            resultType="io.geekidea.boot.system.vo.SysDictTypeVo">
        select *
        from sys_dict_type
        <where>
            <if test="keyword != null and keyword != ''">
                and (code like concat('%',#{keyword},'%')
                or name like concat('%',#{keyword},'%')
                )
            </if>
        </where>
    </select>

</mapper>
