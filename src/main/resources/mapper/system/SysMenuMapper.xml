<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.geekidea.boot.system.mapper.SysMenuMapper">

    <select id="getSysMenuById" resultType="io.geekidea.boot.system.vo.SysMenuVo">
        select *
        from sys_menu
        where id = #{id}
    </select>

    <select id="getPermissionCodesByUserId" resultType="java.lang.String">
        select distinct m.code
        from sys_user u
                 inner join sys_role r
                            on r.id = u.role_id
                 inner join sys_role_menu rm
                            on r.id = rm.role_id
                 inner join sys_menu m
                            on m.id = rm.menu_id
        where u.status = 1
          and m.status = 1
          and m.code is not null
          and u.id = #{userId}
    </select>

    <select id="getSysMenuTreeList" parameterType="io.geekidea.boot.system.query.SysMenuQuery"
            resultType="io.geekidea.boot.system.vo.SysMenuTreeVo">
        select *
        from sys_menu
        <where>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="isShow != null">
                and is_show = #{isShow}
            </if>
            <if test="keyword != null and keyword != ''">
                and (name like concat('%',#{keyword},'%')
                or code like concat('%',#{keyword},'%')
                or route_url like concat('%',#{keyword},'%')
                or component_path like concat('%',#{keyword},'%')
                or route_redirect like concat('%',#{keyword},'%')
                )
            </if>
        </where>
        order by sort
    </select>

    <select id="getNavMenuTreeAllList" resultType="io.geekidea.boot.system.vo.SysNavMenuTreeVo">
        select *
        from sys_menu
        where status = 1
          and type in (1, 2)
        order by sort
    </select>

    <select id="getNavMenuTreeList" resultType="io.geekidea.boot.system.vo.SysNavMenuTreeVo">
        select distinct m.*
        from sys_user u
                 inner join sys_role r
                            on r.id = u.role_id
                 inner join sys_role_menu rm
                            on r.id = rm.role_id
                 inner join sys_menu m
                            on m.id = rm.menu_id
        where u.status = 1
          and m.status = 1
          and type in (1, 2)
          and u.id = #{userId}
        order by m.sort
    </select>

    <select id="getMenuIdsByRoleId" resultType="java.lang.Long">
        select distinct m.id
        from sys_role r
                 inner join sys_role_menu rm
                            on r.id = rm.role_id
                 inner join sys_menu m
                            on m.id = rm.menu_id
        where m.status = 1
          and rm.is_choice = 1
          and r.id = #{roleId}
    </select>

    <select id="getChildrenMenuIds" resultType="java.lang.Long">
        select id
        from sys_menu
        where parent_id in
        <foreach collection="parentIds" item="parentId" open="(" close=")" separator=",">
            #{parentId}
        </foreach>
    </select>

</mapper>
