<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.geekidea.boot.system.mapper.SysFileMapper">

    <select id="getSysFileById" resultType="io.geekidea.boot.system.vo.SysFileVo">
        select *
        from sys_file
        where id = #{id}
    </select>

    <select id="getSysFilePage" parameterType="io.geekidea.boot.system.query.SysFileQuery"
            resultType="io.geekidea.boot.system.vo.SysFileVo">
        select *
        from sys_file
        <where>
            <if test="fileType != null">
                and file_type = #{fileType}
            </if>
            <if test="keyword != null and keyword != ''">
                and (original_file_name like concat('%',#{keyword},'%')
                or file_name like concat('%',#{keyword},'%'))
            </if>
            <if test="createStartTime != null">
                and date(u.create_time) >= date(#{createStartTime})
            </if>
            <if test="createEndTime != null">
                and date(u.create_time) &lt;= date(#{createEndTime})
            </if>
        </where>
    </select>

</mapper>
