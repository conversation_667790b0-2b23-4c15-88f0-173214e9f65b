spring:
  datasource:
    url: **********************************************
    username: postgres
    password: 12345678a@
  redis:
    host: 127.0.0.1
    port: 6379
    password:
    database: 0

# 打印SQL语句和结果集，本地开发环境可开启，线上注释掉
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# file上传配置
file:
  # 本地上传服务配置
  local:
    # 上传文件访问URL前缀
    # 需修改为当前环境对外的ip地址
    # 示例：http://localhost:8888/api/file
    access-url: http://localhost:${server.port}/api/file

# 日志配置，logback-spring.xml中会引用以下变量
logback-spring:
  # 日志文件目录
  path: logs
