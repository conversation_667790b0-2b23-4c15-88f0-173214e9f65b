package $!{serviceImplPackage};

#if($!{generateMapper})
import $!{mapperPackage}.$!{mapperName};
import org.springframework.beans.factory.annotation.Autowired;
#end
import $!{servicePackage}.$!{serviceName};
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * $!{desc}服务接口实现
 *
 * <AUTHOR>
 * @date $!{createDate}
 **/
@Slf4j
@Service
public class $!{serviceImplName} implements $!{serviceName} {

#if($!{generateMapper})
@Autowired
private $!{mapperName} $!{mapperCamelName};
#end

}
