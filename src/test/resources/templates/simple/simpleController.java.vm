package $!{controllerPackage};

#if($!{generateService})
import $!{servicePackage}.$!{serviceName};
import org.springframework.beans.factory.annotation.Autowired;
#end
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * $!{desc}控制器
 *
 * <AUTHOR>
 * @date $!{createDate}
 **/
@Slf4j
@RestController
@RequestMapping("/$!{camelName}")
@Tag(name = "$!{desc}")
public class $!{controllerName} {

#if($!{generateService})
@Autowired
private $!{serviceName} $!{serviceCamelName};
#end

}
