#!/bin/bash

# Maven 3.6.2 Installation Script
# Compatible with Git Bash on Windows and Linux
# Based on install-maven.txt instructions

set -e  # Exit on any error

echo "Starting Maven 3.6.2 installation..."

# Detect operating system
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" || "$OSTYPE" == "win32" ]]; then
    IS_WINDOWS=true
    echo "Detected Windows environment (Git Bash)"
else
    IS_WINDOWS=false
    echo "Detected Linux environment"
fi

# Handle certificate updates (Linux only)
if [ "$IS_WINDOWS" = false ]; then
    echo "Updating certificates..."
    if command -v yum &> /dev/null; then
        yum install -y ca-certificates
    elif command -v apt-get &> /dev/null; then
        apt-get update && apt-get install -y ca-certificates
    else
        echo "Package manager not found, skipping certificate update"
    fi
else
    echo "Skipping certificate update on Windows"
fi

# Set tools directory based on OS
if [ "$IS_WINDOWS" = true ]; then
    # Use a Windows-friendly path in user's home directory
    TOOLS_DIR="$HOME/tools"
else
    TOOLS_DIR="/home/<USER>"
fi

echo "Creating tools directory: $TOOLS_DIR"
mkdir -p "$TOOLS_DIR"

# Navigate to tools directory
cd "$TOOLS_DIR"

# Download Maven 3.6.2
MAVEN_VERSION="3.6.2"
MAVEN_ARCHIVE="apache-maven-${MAVEN_VERSION}-bin.tar.gz"
MAVEN_URL="https://archive.apache.org/dist/maven/maven-3/3.6.2/binaries/${MAVEN_ARCHIVE}"

echo "Downloading Maven ${MAVEN_VERSION}..."
if [ ! -f "$MAVEN_ARCHIVE" ]; then
    if command -v curl &> /dev/null; then
        echo "Using curl to download..."
        curl -L -o "$MAVEN_ARCHIVE" "$MAVEN_URL"
    elif command -v wget &> /dev/null; then
        echo "Using wget to download..."
        wget "$MAVEN_URL"
    else
        echo "Error: Neither curl nor wget is available for downloading"
        exit 1
    fi
else
    echo "Maven archive already exists, skipping download."
fi

# Extract Maven
echo "Extracting Maven..."
tar -zxvf "$MAVEN_ARCHIVE"

# Set Maven home directory
MAVEN_HOME="$TOOLS_DIR/apache-maven-${MAVEN_VERSION}"

# Update environment variables based on OS
echo "Updating environment variables..."

if [ "$IS_WINDOWS" = true ]; then
    # For Windows/Git Bash, update .bashrc or .bash_profile
    if [ -f "$HOME/.bashrc" ]; then
        PROFILE_FILE="$HOME/.bashrc"
    else
        PROFILE_FILE="$HOME/.bash_profile"
    fi
else
    # For Linux, use /etc/profile
    PROFILE_FILE="/etc/profile"
fi

echo "Using profile file: $PROFILE_FILE"

# Check if Maven environment variables already exist
if ! grep -q "MAVEN_HOME" "$PROFILE_FILE" 2>/dev/null; then
    echo "" >> "$PROFILE_FILE"
    echo "# Maven Environment Variables" >> "$PROFILE_FILE"
    echo "export MAVEN_HOME=\"$MAVEN_HOME\"" >> "$PROFILE_FILE"
    echo "export PATH=\"\$PATH:\$MAVEN_HOME/bin\"" >> "$PROFILE_FILE"
    echo "Maven environment variables added to $PROFILE_FILE"
else
    echo "Maven environment variables already exist in $PROFILE_FILE"
fi

# Reload environment variables
echo "Reloading environment variables..."
source "$PROFILE_FILE"

# Set up Maven environment for current session
export MAVEN_HOME="$MAVEN_HOME"
export PATH="$PATH:$MAVEN_HOME/bin"

# Verify Maven installation
echo "Verifying Maven installation..."
if command -v mvn &> /dev/null; then
    mvn -v
else
    echo "Maven command not found in PATH. You may need to restart your terminal."
fi

# Configure Aliyun Maven mirror
SETTINGS_FILE="$MAVEN_HOME/conf/settings.xml"
echo "Configuring Aliyun Maven mirror in $SETTINGS_FILE..."

# Create backup of original settings.xml
cp "$SETTINGS_FILE" "${SETTINGS_FILE}.backup"

# Add Aliyun mirror configuration
# This uses a simple approach - in production you might want more sophisticated XML editing
if ! grep -q "aliyun-maven" "$SETTINGS_FILE"; then
    # For Windows compatibility, use a more portable sed approach
    if [ "$IS_WINDOWS" = true ]; then
        # Create a temporary file with the mirror configuration
        TEMP_MIRROR=$(mktemp)
        cat > "$TEMP_MIRROR" << 'EOF'
    <mirror>
      <id>aliyun-maven</id>
      <mirrorOf>central</mirrorOf>
      <name>aliyun maven</name>
      <url>http://maven.aliyun.com/repository/public</url>
    </mirror>
EOF
        # Insert the mirror configuration after the <mirrors> tag
        awk '/<mirrors>/ {print; while ((getline line < "'"$TEMP_MIRROR"'") > 0) print line; next} 1' "$SETTINGS_FILE" > "${SETTINGS_FILE}.tmp"
        mv "${SETTINGS_FILE}.tmp" "$SETTINGS_FILE"
        rm "$TEMP_MIRROR"
    else
        # Use sed for Linux
        sed -i '/<mirrors>/a\
    <mirror>\
      <id>aliyun-maven</id>\
      <mirrorOf>central</mirrorOf>\
      <name>aliyun maven</name>\
      <url>http://maven.aliyun.com/repository/public</url>\
    </mirror>' "$SETTINGS_FILE"
    fi
    echo "Aliyun Maven mirror configuration added."
else
    echo "Aliyun Maven mirror already configured."
fi

# Initialize Maven and download necessary dependencies
echo "Initializing Maven and downloading necessary dependencies..."
if command -v mvn &> /dev/null; then
    mvn help:system
else
    echo "Maven command not available. Please restart your terminal and run 'mvn help:system' manually."
fi

echo ""
echo "Maven installation completed successfully!"
echo "Maven Home: $MAVEN_HOME"
echo "Maven Version: $MAVEN_VERSION"

if [ "$IS_WINDOWS" = true ]; then
    echo ""
    echo "For Windows/Git Bash users:"
    echo "1. Restart your Git Bash terminal to load the new environment variables"
    echo "2. Or run: source $PROFILE_FILE"
    echo "3. Verify installation with: mvn -v"
else
    echo ""
    echo "For Linux users:"
    echo "1. To use Maven in new terminal sessions, run: source /etc/profile"
    echo "2. Or restart your terminal"
    echo "3. Verify installation with: mvn -v"
fi
