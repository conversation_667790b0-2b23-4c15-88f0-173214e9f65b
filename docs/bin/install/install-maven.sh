#!/bin/bash

# Maven 3.6.2 Installation Script
# Based on install-maven.txt instructions

set -e  # Exit on any error

echo "Starting Maven 3.6.2 installation..."

# Update certificates to resolve wget https download issues
echo "Updating certificates..."
yum install -y ca-certificates

# Create tools directory if it doesn't exist
TOOLS_DIR="/home/<USER>"
echo "Creating tools directory: $TOOLS_DIR"
mkdir -p $TOOLS_DIR

# Navigate to tools directory
cd $TOOLS_DIR

# Download Maven 3.6.2
MAVEN_VERSION="3.6.2"
MAVEN_ARCHIVE="apache-maven-${MAVEN_VERSION}-bin.tar.gz"
MAVEN_URL="https://archive.apache.org/dist/maven/maven-3/3.6.2/binaries/${MAVEN_ARCHIVE}"

echo "Downloading Maven ${MAVEN_VERSION}..."
if [ ! -f "$MAVEN_ARCHIVE" ]; then
    wget $MAVEN_URL
else
    echo "Maven archive already exists, skipping download."
fi

# Extract Maven
echo "Extracting Maven..."
tar -zxvf $MAVEN_ARCHIVE

# Set Maven home directory
MAVEN_HOME="$TOOLS_DIR/apache-maven-${MAVEN_VERSION}"

# Update environment variables
echo "Updating environment variables..."
PROFILE_FILE="/etc/profile"

# Check if Maven environment variables already exist
if ! grep -q "MAVEN_HOME" $PROFILE_FILE; then
    echo "" >> $PROFILE_FILE
    echo "# Maven Environment Variables" >> $PROFILE_FILE
    echo "export MAVEN_HOME=$MAVEN_HOME" >> $PROFILE_FILE
    echo "export PATH=\$PATH:\$MAVEN_HOME/bin" >> $PROFILE_FILE
    echo "Maven environment variables added to $PROFILE_FILE"
else
    echo "Maven environment variables already exist in $PROFILE_FILE"
fi

# Reload environment variables
echo "Reloading environment variables..."
source $PROFILE_FILE

# Verify Maven installation
echo "Verifying Maven installation..."
mvn -v

# Configure Aliyun Maven mirror
SETTINGS_FILE="$MAVEN_HOME/conf/settings.xml"
echo "Configuring Aliyun Maven mirror in $SETTINGS_FILE..."

# Create backup of original settings.xml
cp $SETTINGS_FILE "${SETTINGS_FILE}.backup"

# Add Aliyun mirror configuration
# This uses a simple approach - in production you might want more sophisticated XML editing
if ! grep -q "aliyun-maven" $SETTINGS_FILE; then
    # Find the mirrors section and add the aliyun mirror
    sed -i '/<mirrors>/a\
    <mirror>\
      <id>aliyun-maven</id>\
      <mirrorOf>central</mirrorOf>\
      <name>aliyun maven</name>\
      <url>http://maven.aliyun.com/repository/public</url>\
    </mirror>' $SETTINGS_FILE
    echo "Aliyun Maven mirror configuration added."
else
    echo "Aliyun Maven mirror already configured."
fi

# Initialize Maven and download necessary dependencies
echo "Initializing Maven and downloading necessary dependencies..."
mvn help:system

echo "Maven installation completed successfully!"
echo "Maven Home: $MAVEN_HOME"
echo "To use Maven in new terminal sessions, run: source /etc/profile"
