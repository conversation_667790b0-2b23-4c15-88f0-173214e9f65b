# 解决wget下载https时错误问题，更新证书
yum install -y ca-certificates

# 进入到目录，maven下载目录
cd /home/<USER>

# 下载maven3.6.2版本，下载太慢可本地下载后上传至服务器目录
wget https://archive.apache.org/dist/maven/maven-3/3.6.2/binaries/apache-maven-3.6.2-bin.tar.gz

# 解压
tar -zxvf apache-maven-3.6.2-bin.tar.gz

# 修改环境变量
vi /etc/profile

# 将如下配置添加到文件末尾出
export MAVEN_HOME=/home/<USER>/apache-maven-3.6.2
export PATH=$PATH:$MAVEN_HOME/bin

# 刷新环境变量
source /etc/profile

# 查看maven版本
mvn -v
Apache Maven 3.6.2 (40f52333136460af0dc0d7232c0dc0bcf0d9e117; 2019-08-27T23:06:16+08:00)
Maven home: /home/<USER>/apache-maven-3.6.2
Java version: 1.8.0_372, vendor: Red Hat, Inc., runtime: /usr/lib/jvm/java-1.8.0-openjdk-1.8.0.372.b07-1.el7_9.x86_64/jre
Default locale: en_US, platform encoding: UTF-8
OS name: "linux", version: "3.10.0-862.el7.x86_64", arch: "amd64", family: "unix"

# 设置settings.xml
vim /home/<USER>/apache-maven-3.6.2/conf/settings.xml

# 修改为aliyun maven镜像，在mirrors标签中添加如下配置

<mirror>
    <id>aliyun-maven</id>
    <mirrorOf>central</mirrorOf>
    <name>aliyun maven</name>
    <url>http://maven.aliyun.com/repository/public</url>
</mirror>

# 初始化下载maven必要依赖
mvn help:system

